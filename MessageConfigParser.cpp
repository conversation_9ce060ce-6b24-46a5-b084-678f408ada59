// Zweck:
// Implementierung des Parsers für message_config.txt
// Aktuell zuständig für die Prüfung des Datei-Headers "MESSAGE"

// Bereits implementiert:
//     checkFile(...) prüft, ob Datei geöffnet werden kann und der MAGIC Header korrekt ist
//     Rückgabe bei Fehlern erfolgt mit passender Fehlermeldung und false.

#include "MessageConfigParser.hpp"
#include <fstream>
#include <iostream>

bool MessageConfigParser::checkFile(const std::string& path, const std::string& magic) {
    std::ifstream file(path);
    if (!file.is_open()) {
        std::cout << "[ERROR] Invalid file (" << path << ")." << std::endl;
        return false;
    }


    std::string line;
    std::getline(file, line);
    if (line != magic) {
        std::cout << "[ERROR] Invalid file (" << path << ")." << std::endl;
        return false;
    }

    return true;
}

bool MessageConfigParser::parseMessages(const std::string& path) {
    std::ifstream file(path);
    if (!file.is_open()) return false;

    std::string line;
    std::getline(file, line); // skip MESSAGE

    while (std::getline(file, line)) {
        size_t colon = line.find(':');
        if (colon == std::string::npos) continue;

        std::string key = line.substr(0, colon);
        std::string value = line.substr(colon + 1);
        messages[key] = value;
    }

    return true;
}

void MessageConfigParser::printMessage(const std::string& key) const {
    auto it = messages.find(key);
    if (it == messages.end()) return;

    const std::string& value = it->second;

    if (key.rfind("E_", 0) == 0) std::cout << "[ERROR] ";
    else if (key.rfind("I_", 0) == 0) std::cout << "[INFO] ";

    std::cout << value << std::endl;
}
////
const std::string& MessageConfigParser::getMessage(const std::string& key) const {
    static const std::string defaultMessage = "[Message not found]";
    auto it = messages.find(key);
    if (it != messages.end()) {
        return it->second;
    }
    return defaultMessage;
}
