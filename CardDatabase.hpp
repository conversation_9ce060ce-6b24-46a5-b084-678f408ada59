#ifndef CARDDATABASE_HPP
#define CARDDATABASE_HPP
#include <unordered_map>
#include <memory>
#include "Card.hpp"
#include "CreatureCard.hpp"
#include "SpellCard.hpp"

class CardDatabase
{
	private:

		std::unordered_map<std::string, std::shared_ptr<Card>> cards_;

	public:
		CardDatabase();

		virtual ~CardDatabase() = default;

		std::shared_ptr<Card> getCardById(std::string id) const;
};

#endif////