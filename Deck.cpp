
#include "Deck.hpp"
#include "CardDatabase.hpp"   
#include "Card.hpp"           
#include <memory>             
#include <algorithm>
#include <random>
#include <iostream>


Deck::Deck(const std::vector<std::string>& cardIds, const CardDatabase& db)
{
   for (const auto& id: cardIds) 
   {
    std::shared_ptr<Card> card = db.getCardById(id);
    if(card)
    {
        cards_.push_back(card);
    }
    else
    {
        std::cout <<"Unkwon card ID (class Deck cpp)" << id << std::endl;
    }
   }
}

std::shared_ptr <Card> Deck::draw()
{
    if (cards_.empty())
    {
        return nullptr; //-------------------------------------------------Hier NOCH  ERROR MESSAGE SPÄTER DAZU
    }
    std::shared_ptr<Card> topCard = cards_.back();
    cards_.pop_back();
    return topCard;
}

bool Deck::isEmpty() const
{
    return cards_.empty();
}

void Deck::shuffle() 
{
    std::random_device random_device;
    std::mt19937 gen(random_device());
    std::shuffle(cards_.begin(), cards_.end(),gen);
}

int Deck::size() const
{
    return static_cast<int>(cards_.size());
}////