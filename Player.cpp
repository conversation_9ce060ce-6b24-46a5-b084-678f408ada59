#include "Player.hpp"
#include <algorithm>
#include <chrono>
#include "Deck.hpp"
 
Player::Player(int initialHealth, int initialManaPoolSize)
    : health_(initialHealth),
      manaPoolSize_(initialManaPoolSize),
      currentMana_(initialManaPoolSize)
{
}

Player::Player(const std::vector<std::string> &cardIds, const CardDatabase &cardDb, int initialHealth, int initialManaPoolSize)
    : health_(initialHealth),
      manaPoolSize_(initialManaPoolSize),
      currentMana_(initialManaPoolSize)
{
    initializeDeck(cardIds, cardDb);
}

// Mana-System
void Player::increaseManaPoolSize()
{
    manaPoolSize_++;
}

void Player::refillMana()
{
    currentMana_ = manaPoolSize_;
}

bool Player::spendMana(int amount)
{
    if (amount > currentMana_)
    {
        return false;
    }

    currentMana_ -= amount;
    return true;
}

int Player::getCurrentMana() const
{
    return currentMana_;
}

int Player::getManaPoolSize() const
{
    return manaPoolSize_;
}

// Gesundheit
int Player::getHealth() const
{
    return health_;
}

void Player::takeDamage(int damage)
{
    health_ -= damage;
    if (health_ < 0)
    {
        health_ = 0;
    }
}

void Player::heal(int amount)
{
    health_ += amount;
}

void Player::changeHealth(int amount)
{
    health_ += amount;
    if (health_ < 0)
    {
        health_ = 0;
    }
}

// Deck-Management
void Player::initializeDeck(const std::vector<std::string> &cardIds, const CardDatabase &cardDb)
{
    for (const auto &cardId : cardIds)
    {
        auto card = cardDb.getCardById(cardId);
        if (card)
        {
            deck_.push_back(card);
        }
    }
}

void Player::shuffleDeck()
{
    unsigned seed = static_cast<unsigned>(std::chrono::system_clock::now().time_since_epoch().count());
    std::shuffle(deck_.begin(), deck_.end(), std::default_random_engine(seed));
}

// Karten ziehen und spielen
std::shared_ptr<Card> Player::drawCard()
{
    if (deck_.empty())
    {
        return nullptr;
    }
//    std::shared_ptr<Card> card = deck_.back();
    //deck_.pop_back();
    //Geändert zu:

    std::shared_ptr<Card> card = deck_.front();
    deck_.erase(deck_.begin());

    hand_.push_back(card);
    return card;
}

std::shared_ptr<Card> Player::findCardInHand(const std::string &cardId) const
{
    auto it = std::find_if(hand_.begin(), hand_.end(),
                           [&cardId](const std::shared_ptr<Card> &card)
                           {
                               return card->getId() == cardId;
                           });

    if (it != hand_.end())
    {
        return *it;
    }

    return nullptr;
}

std::shared_ptr<Card> Player::removeCardFromHand(const std::string &cardId)
{
    auto it = std::find_if(hand_.begin(), hand_.end(),
                           [&cardId](const std::shared_ptr<Card> &card)
                           {
                               return card->getId() == cardId;
                           });

    if (it != hand_.end())
    {
        std::shared_ptr<Card> card = *it;
        hand_.erase(it);
        return card;
    }

    return nullptr;
}

// Friedhof-Management
void Player::addCardToGraveyard(std::shared_ptr<Card> card)
{
    auto creatureCard = std::dynamic_pointer_cast<CreatureCard>(card);
    if (creatureCard)
    {
        graveyard_.add(creatureCard);
    }
}

std::shared_ptr<Card> Player::findCardInGraveyard(const std::string &cardId) const
{
    for (auto &card : graveyard_.getCards())
    {
        if (card->getId() == cardId)
        {
            return card;
        }
    }
    return nullptr;
}

std::shared_ptr<Card> Player::removeCardFromGraveyard(const std::string &cardId)
{
    // Silence unused parameter warning
    (void)cardId;

    // This is a simplified implementation
    // In a real implementation, you would need to find the card in the graveyard
    // and remove it properly
    return nullptr;
}

// Getter
int Player::getDeckSize() const
{
    return static_cast<int>(deck_.size()) ;
}

int Player::getHandSize() const
{
    return static_cast<int>(hand_.size());
}

int Player::getGraveyardSize() const
{
    return graveyard_.size();
}

const std::vector<std::shared_ptr<Card>> &Player::getHand() const
{
    return hand_;
}

const FieldZone& Player::getFieldZone() const {
    return fieldZone_;
}

const BattleZone& Player::getBattleZone() const {
    return battleZone_;
}


FieldZone& Player::getFieldZone() {
    return fieldZone_;
}

BattleZone& Player::getBattleZone() {
    return battleZone_;
}

Graveyard &Player::getGraveyard()
{
    return graveyard_;
}

void Player::clearHand()
{
    hand_.clear();
}

void Player::addCardToDeckBottom(std::shared_ptr<Card> card)
{
    deck_.push_back(card);
}