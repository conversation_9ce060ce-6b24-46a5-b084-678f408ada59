#include "Graveyard.hpp"

void Graveyard::add(std::shared_ptr<CreatureCard> creature)
{
    if (creature)
    {
        cards_.push_back(creature);
    }
}

std::shared_ptr<CreatureCard> Graveyard::top() const
{
    if (!cards_.empty())
    {
        return cards_.back();
    }
    return nullptr;
}

std::shared_ptr<CreatureCard> Graveyard::removeTop()
{
    if (cards_.empty())
    {
        return nullptr;
    }
    std::shared_ptr<CreatureCard> topCard = cards_.back();
    cards_.pop_back();
    return topCard;
}

bool Graveyard::isEmpty() const
{
    return cards_.empty();
}

int Graveyard::size() const
{
    return static_cast<int>(cards_.size());
}

const std::vector<std::shared_ptr<CreatureCard>> &Graveyard::getCards() const
{
    return cards_;
} ////