#include "CreatureCard.hpp"
#include "Trait.hpp"

CreatureCard::CreatureCard(std::string name, std::string id, int manaCost,
             int baseHealth, int baseAttack, std::vector<Trait> baseTraits)
    :Card(name, id, manaCost),
     baseHealth_(baseHealth), currentHealth_(baseHealth),
     baseAttack_(baseAttack), currentAttack_(baseAttack),
     baseTraits_(baseTraits), currentTraits_(baseTraits){}

//------------------------------------------Getter-setter
int CreatureCard::getBaseHealth() const
{
	return baseHealth_;
}

int CreatureCard::getCurrentHealth() const
{
	return currentHealth_;
}

int CreatureCard::getBaseAttack() const
{
	return baseAttack_;
}

int CreatureCard::getCurrentAttack() const
{
	return currentAttack_;
}

std::vector <Trait> CreatureCard::getBaseTraits() const
{
	return baseTraits_;
}

std::vector <Trait> CreatureCard::getCurrentTraits() const
{
	return currentTraits_;
}

void CreatureCard::setCurrentHealth(int health)
{
	currentHealth_ = health;
}

void CreatureCard::setCurrentAttack(int attack)
{
	currentAttack_ = attack;
}

void CreatureCard::setCurrentTraits(const std::vector <Trait>& traits)
{
	currentTraits_ = traits;
}

//-------------------------------------------------methods
void CreatureCard::takeDamage(int damage)
{
	currentHealth_ = currentHealth_ - damage;
	if (currentHealth_ < 0)
	{
		currentHealth_ = 0;
	}
}

void CreatureCard::resetToBaseStats()
{
	currentHealth_ = baseHealth_;
	currentAttack_ = baseAttack_;
	currentTraits_ = baseTraits_;
}////

const std::vector<Trait>& CreatureCard::getTraits() const {
    return currentTraits_;
}
std::vector<Trait>& CreatureCard::getTraits() {
    return currentTraits_;
}