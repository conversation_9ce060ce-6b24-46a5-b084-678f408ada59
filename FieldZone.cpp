#include "FieldZone.hpp"
#include <iostream>
#include <memory>

bool FieldZone::placeCard(int slotIndex, std::shared_ptr<CreatureCard> card)
{
    if (slotIndex < 0 || slotIndex >= 7 || !card || slots_[slotIndex])
    {
        std::cout << " ERROR 1 PLACING CARD FIELD ZONE" << std::endl;
        return false;
    }
    slots_[slotIndex] = card;
    return true;
}

std::shared_ptr<CreatureCard> FieldZone::removeCard(int slotIndex)
{
    if (slotIndex < 0 || slotIndex >= 7 || !slots_[slotIndex])
    {
        std::cout << " ERROR 2 REMOVING CARD FIELD ZONE" << std::endl;
        return nullptr;
    }
    auto removed = slots_[slotIndex];
    slots_[slotIndex] = nullptr;
    return removed;
}

std::shared_ptr<CreatureCard> FieldZone::getCard(int slotIndex) const
{
    if (slotIndex < 0 || slotIndex >= 7)
    {
        std::cout << "ERROR 3 GETTING CARD FIELD ZONE" << std::endl;
        return nullptr;
    }
    return slots_[slotIndex];
}

bool FieldZone::isSlotEmpty(int slotIndex) const
{
    return slotIndex >= 0 && slotIndex < 7 && !slots_[slotIndex];
}