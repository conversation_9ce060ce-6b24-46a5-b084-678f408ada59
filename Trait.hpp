#ifndef TRAIT_HPP
#define TRAIT_HPP

#include <string>
#include <ostream>

enum class Trait {
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    Challenger,
    FirstStrike,
    <PERSON><PERSON>,
    <PERSON>steal,
    Poisoned,
    Regenerate,
    Temporary,
    Undying,
    Venomous
};

std::string to_string_info(Trait trait);
std::string to_string_board(Trait trait);
std::ostream& operator<<(std::ostream& os, Trait trait); // <-- обязательно

#endif
