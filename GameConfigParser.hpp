// Zweck:
// Header-Datei für den Parser der Game-Konfigurationsdatei (game_config.txt)
// Deklariert Methoden zum Überprüfen und Einlesen der Spielparameter

// Bereits implementiert:
//     checkFile(...) zur Überprüfung der Datei und des MAGIC Headers ("GAME")
//     Getter-Methoden für Health, MaxRounds, DeckSize, ManaPool und Decks beider Spieler

#ifndef GAMECONFIGPARSER_HPP
#define GAMECONFIGPARSER_HPP

#include <string>
#include <vector>

class GameConfigParser {
public:
    bool checkFile(const std::string& path, const std::string& magic);
    bool parseFullConfig(const std::string& path);

    int getHealth() const;
    int getMaxRounds() const;
    int getDeckSize() const;
    int getManaPoolSize() const;
    std::vector<std::string> getPlayer1Deck() const;
    std::vector<std::string> getPlayer2Deck() const;

private:
    int health = 0;
    int maxRounds = 0;
    int deckSize = 0;
    int manaPoolSize = 0;
    std::vector<std::string> deck1;
    std::vector<std::string> deck2;
};

#endif
////