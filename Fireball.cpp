#include "Fireball.hpp"
#include <iostream>

Fireball::Fireball()
    : GeneralSpell("Fireball", "FIRBL", 5) {}

void Fireball::execute(Player& current, Player& opponent)
{
for (int i = 0; i <7; ++i)
{
	auto card = opponent.getFieldZone().getCard(i);
	auto creature = std::dynamic_pointer_cast<CreatureCard>(card);
	if(creature)
	{
		creature->takeDamage(2);
	}
}

for(int i = 0; i < 7; ++i)
{
	auto card = opponent.getBattleZone().getCard(i);
	auto creature = std::dynamic_pointer_cast<CreatureCard>(card);
	if(creature)
	{
		creature->takeDamage(2);
	}
}
}
////