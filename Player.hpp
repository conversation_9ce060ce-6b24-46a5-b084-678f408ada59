#ifndef PLAYER_HPP
#define PLAYER_HPP

#include <vector>
#include <memory>
#include <string>
#include <random>
#include "Card.hpp"
#include "CardDatabase.hpp"
#include "FieldZone.hpp"
#include "BattleZone.hpp"
#include "Graveyard.hpp"

 
class Player
{
private:
    int health_;
    int manaPoolSize_;
    int currentMana_;

    std::vector<std::shared_ptr<Card>> deck_;
    std::vector<std::shared_ptr<Card>> hand_;
    Graveyard graveyard_;
    FieldZone fieldZone_;
    BattleZone battleZone_;

public:
    Player() = default;
    Player(int initialHealth, int initialManaPoolSize);
    Player(const std::vector<std::string> &cardIds, const CardDatabase &cardDb, int initialHealth, int initialManaPoolSize);
    
    void increaseManaPoolSize();
    void refillMana();
    bool spendMana(int amount);
    int getCurrentMana() const;
    int getManaPoolSize() const;

    int getHealth() const;
    void takeDamage(int damage);
    void heal(int amount);
    void changeHealth(int amount);

    void initializeDeck(const std::vector<std::string> &cardIds, const CardDatabase &cardDb);
    void shuffleDeck();

    std::shared_ptr<Card> drawCard();
    std::shared_ptr<Card> findCardInHand(const std::string &cardId) const;
    std::shared_ptr<Card> removeCardFromHand(const std::string &cardId);
    void clearHand(); 
    void addCardToDeckBottom(std::shared_ptr<Card> card);

    void addCardToGraveyard(std::shared_ptr<Card> card);
    std::shared_ptr<Card> findCardInGraveyard(const std::string &cardId) const;
    std::shared_ptr<Card> removeCardFromGraveyard(const std::string &cardId);

    int getDeckSize() const;
    int getHandSize() const;
    int getGraveyardSize() const;
    const std::vector<std::shared_ptr<Card>> &getHand() const;

    FieldZone &getFieldZone();
    BattleZone &getBattleZone();
    Graveyard &getGraveyard();

    const FieldZone& getFieldZone() const;
const BattleZone& getBattleZone() const;

};


#endif
////