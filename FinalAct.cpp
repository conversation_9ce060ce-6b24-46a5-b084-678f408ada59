#include "FinalAct.hpp"
#include "Trait.hpp"

FinalAct::FinalAct()
    : TargetSpell("Final Act", "FINAL", 3) {}

void FinalAct::applyEffect(std::shared_ptr<CreatureCard> target)
{
    std::vector<Trait> traits = target->getCurrentTraits();
    traits.push_back(Trait::Brutal);
    traits.push_back(Trait::<PERSON><PERSON>);
    traits.push_back(Trait::Temporary);
    target->setCurrentTraits(traits);
    target->setCurrentAttack(target->getCurrentAttack() + 3);
}
////