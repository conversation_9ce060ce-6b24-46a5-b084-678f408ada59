#include <iostream>
#include "Game.hpp"

int main(int argc, char* argv[]) {
    if (argc != 3) {
        std::cout << "[ERROR] Wrong number of parameters." << std::endl;
        return 2;
    }

    Game game;
    if (!game.initialize(argv[1], argv[2])) {
        return 3;
    }

//     // >>> Debugging line:
// std::cout << "[DEBUG] P1 deck after init: " 
//           << game.getPlayer1().getDeckSize() 
//           << ", mana: " 
//           << game.getPlayer1().getCurrentMana() 
//           << "/" 
//           << game.getPlayer1().getManaPoolSize() 
//           << "\n";

    // запускаем игру
    game.start();

    return 0;
}
