#include "Trait.hpp"
#include <ostream>

// Полное имя трейта (для info-команды)
std::string to_string_info(Trait trait)
{
    switch (trait)
    {
    case Trait::Brutal:       return "Brutal";
    case Trait::Challenger:   return "Challenger";
    case Trait::FirstStrike:  return "FirstStrike";
    case Trait::Haste:        return "Has<PERSON>";
    case Trait::Lifesteal:    return "Lifesteal";
    case Trait::Poisoned:     return "Poisoned";
    case Trait::Regenerate:   return "Regenerate";
    case Trait::Temporary:    return "Temporary";
    case Trait::Undying:      return "Undying";
    case Trait::Venomous:     return "Venomous";
    case Trait::Flying:       return "Flying";
    default:                  return "Unknown TRAIT";
    }
}

// Сокращённое обозначение (для игрового поля)
std::string to_string_board(Trait trait)
{
    switch (trait)
    {
    case Trait::Brutal:       return "B";
    case Trait::Challenger:   return "C";
    case Trait::FirstStrike:  return "F";
    case Trait::Haste:        return "H";
    case Trait::Lifesteal:    return "L";
    case Trait::Poisoned:     return "P";
    case Trait::Regenerate:   return "R";
    case Trait::Temporary:    return "T";
    case Trait::Undying:      return "U";
    case Trait::Venomous:     return "V";
    case Trait::Flying:       return "Y";
    default:                  return "?";
    }
}



std::ostream& operator<<(std::ostream& os, Trait trait) {
    os << to_string_info(trait);
    return os;
}