#include "CardDatabase.hpp"
// General Spells
#include "BattleCry.hpp"
#include "Meteor.hpp"
#include "Fireball.hpp"
#include "Clone.hpp"
#include "Curse.hpp"
#include "Shock.hpp"
#include "Mobilize.hpp"
#include "RapidRush.hpp"
#include "Shield.hpp"
#include "Amputate.hpp"
#include "FinalAct.hpp"
#include "Loyalty.hpp"
#include "Zombify.hpp"
#include "Bloodlust.hpp"





#include "HeroicMemory.hpp"
#include "Revive.hpp"
CardDatabase::CardDatabase()
{
	//------Creature Cards-------------
	cards_["AGRAT"] = std::make_shared<CreatureCard>("Angry Rat", "AGRAT", 1, 1, 1, std::vector<Trait>{Trait::Haste});
	cards_["CADET"] = std::make_shared<CreatureCard>("Cadet", "CADET", 1, 1, 2, std::vector<Trait>{});
	cards_["FARMR"] = std::make_shared<CreatureCard>("Farmer", "FARMR", 1, 2, 1, std::vector<Trait>{Trait::Haste});
	cards_["SQIRL"] = std::make_shared<CreatureCard>("Squirrel Soldier", "SQIRL", 1, 1, 1, std::vector<Trait>{Trait::FirstStrike});
	cards_["FSHLD"] = std::make_shared<CreatureCard>("Floating Shield", "FSHLD", 2, 8,0, std::vector<Trait>{});
	cards_["NITMR"] = std::make_shared<CreatureCard>("Nightmare", "NITMR", 2, 1, 5, std::vector<Trait>{Trait::Haste, Trait::Temporary});
	cards_["SOLDR"] = std::make_shared<CreatureCard>("Soldier", "SOLDR", 2, 4, 3, std::vector<Trait>{});
	cards_["SNAKE"] = std::make_shared<CreatureCard>("Snake", "SNAKE", 2, 1, 2, std::vector<Trait>{Trait::Venomous});
	cards_["HWOLF"] = std::make_shared<CreatureCard>("Hungry Wolf", "HWOLF", 2, 2, 3, std::vector<Trait>{Trait::Brutal});
	cards_["ZOMBI"] = std::make_shared<CreatureCard>("Zombie", "ZOMBI", 2, 2, 2, std::vector<Trait>{Trait::Undying});
	cards_["ASASN"] = std::make_shared<CreatureCard>("Assassin", "ASASN", 3, 2, 5, std::vector<Trait>{Trait::FirstStrike});
	cards_["CVLRY"] = std::make_shared<CreatureCard>("Cavalry", "CVLRY", 3, 4, 4, std::vector<Trait>{Trait::Haste});
	cards_["GLDTR"] = std::make_shared<CreatureCard>("Gladiator", "GLDTR", 3, 3,5, std::vector<Trait>{Trait::Challenger});
	cards_["KNGHT"] = std::make_shared<CreatureCard>("Knight", "KNGHT", 3, 6, 4, std::vector<Trait>{Trait::Haste});
	cards_["VAMPS"] = std::make_shared<CreatureCard>("Vampire Soldier", "VAMPS", 3, 3, 4, std::vector<Trait>{Trait::Lifesteal});
	cards_["ALCHM"] = std::make_shared<CreatureCard>("Alchemist", "ALCHM", 4, 6, 4, std::vector<Trait>{Trait::Venomous});
	cards_["TUTOR"] = std::make_shared<CreatureCard>("Evil Tutor", "TUTOR", 4, 4, 5, std::vector<Trait>{Trait::Challenger, Trait::Lifesteal});
	cards_["TURTL"] = std::make_shared<CreatureCard>("Giant Turtle", "TURTL", 4, 11, 3, std::vector<Trait>{});
	cards_["NINJA"] = std::make_shared<CreatureCard>("Ninja", "NINJA", 4, 4, 6, std::vector<Trait>{Trait::FirstStrike, Trait::Haste});
	cards_["GUARD"] = std::make_shared<CreatureCard>("Eternal Guardian", "GUARD", 5, 5, 5, std::vector<Trait>{Trait::Haste, Trait::Undying});
	cards_["RAPTR"] = std::make_shared<CreatureCard>("Raptor", "RAPTR", 5, 4, 7, std::vector<Trait>{Trait::Brutal, Trait::FirstStrike});
	cards_["WRLCK"] = std::make_shared<CreatureCard>("Warlock", "WRLCK", 5, 7, 4, std::vector<Trait>{Trait::Lifesteal, Trait::Venomous});
	cards_["GOLEM"] = std::make_shared<CreatureCard>("Golem", "GOLEM", 6, 12, 5, std::vector<Trait>{Trait::Regenerate});
	cards_["HYDRA"] = std::make_shared<CreatureCard>("Hydra", "HYDRA", 6, 6, 7, std::vector<Trait>{Trait::Regenerate, Trait::Undying});
	cards_["KINGV"] = std::make_shared<CreatureCard>("King V", "KINGV", 6, 11, 6, std::vector<Trait>{Trait::Challenger, Trait::Haste});
	cards_["LLICH"] = std::make_shared<CreatureCard>("Likeable Lich", "LLICH", 7, 6, 9, std::vector<Trait>{Trait::Lifesteal, Trait::Undying});
	cards_["T_REX"] = std::make_shared<CreatureCard>("T-Rex", "T_REX", 7, 9, 13, std::vector<Trait>{Trait::Brutal});
	cards_["VAMPL"] = std::make_shared<CreatureCard>("Vampire Lord", "VAMPL", 7, 7, 10, std::vector<Trait>{Trait::Challenger, Trait::Lifesteal});
	cards_["ANGEL"] = std::make_shared<CreatureCard>("Angel", "ANGEL", 8, 14, 9, std::vector<Trait>{Trait::Haste});
	cards_["DRAGN"] = std::make_shared<CreatureCard>("Dragon", "DRAGN", 8, 10, 13, std::vector<Trait>{Trait::Brutal, Trait::Challenger});
	cards_["SLAYR"] = std::make_shared<CreatureCard>("Slayer", "SLAYR", 8, 6, 15, std::vector<Trait>{Trait::FirstStrike, Trait::Haste});
	cards_["D_GOD"] = std::make_shared<CreatureCard>("Demi-God", "D_GOD", 9, 15, 15, std::vector<Trait>{Trait::Regenerate, Trait::Undying});
	cards_["DEVIL"] = std::make_shared<CreatureCard>("Devil", "DEVIL", 9, 7, 16, std::vector<Trait>{Trait::Brutal, Trait::FirstStrike});

	// ----- General Spells -----
	cards_["BTLCY"] = std::make_shared<BattleCry>();
	cards_["METOR"] = std::make_shared<Meteor>();
	cards_["FIRBL"] = std::make_shared<Fireball>();

	// ----- Target Spells -----
	cards_["CLONE"] = std::make_shared<Clone>();
	cards_["CURSE"] = std::make_shared<Curse>();
	cards_["SHOCK"] = std::make_shared<Shock>();
	cards_["MOBLZ"] = std::make_shared<Mobilize>();
	cards_["RRUSH"] = std::make_shared<RapidRush>();
	cards_["SHILD"] = std::make_shared<Shield>();
	cards_["AMPUT"] = std::make_shared<Amputate>();
	cards_["FINAL"] = std::make_shared<FinalAct>();
	cards_["LYLTY"] = std::make_shared<Loyalty>();
	cards_["ZMBFY"] = std::make_shared<Zombify>();
	cards_["BLOOD"] = std::make_shared<Bloodlust>();

	// ----- Graveyard Spells -----
	cards_["MEMRY"] = std::make_shared<HeroicMemory>();
	cards_["REVIV"] = std::make_shared<Revive>();

}

std::shared_ptr<Card> CardDatabase::getCardById(std::string id) const
{
	auto it = cards_.find(id);
	if(it != cards_.end())
	{
		return it->second;
	}
	else
	{
		return nullptr;
	}   
}///