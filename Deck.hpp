#ifndef DECK_HPP
#define DECK_HPP

#include <vector>
#include <string>
#include <memory>
#include "Card.hpp"
#include "CardDatabase.hpp"

class Deck {
private:
    std::vector<std::shared_ptr<Card>> cards_;

public:
    Deck(const std::vector<std::string>& cardIds, const CardDatabase& db);

    std::shared_ptr<Card> draw();
    bool isEmpty() const;
    void shuffle();
    int size() const;
};

#endif // DECK_HPP
////