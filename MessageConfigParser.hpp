// Zweck:
// Header-Date<PERSON> für den Parser der Nachrichtenkonfigurationsdatei (message_config.txt).
// Deklariert die Methode zum Überprüfen des MAGIC Headers und Laden der Nachrichten.

// Bereits implementiert:
//     checkFile(...) als öffentliche Methode zur Validierung der Datei mit dem Header "MESSAGE".
//     Struktur vorgesehen für spätere Speicherung der Nachrichten 
#ifndef MESSAGECONFIGPARSER_HPP
#define MESSAGECONFIGPARSER_HPP

#include <string>
#include <unordered_map>

class MessageConfigParser {
public:
    bool checkFile(const std::string& path, const std::string& magic);
    bool parseMessages(const std::string& path);
    void printMessage(const std::string& key) const;

    
    const std::string& getMessage(const std::string& key) const;

private:
    std::unordered_map<std::string, std::string> messages;
};

#endif
