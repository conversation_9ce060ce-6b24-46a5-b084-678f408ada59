#include "Meteor.hpp"
#include <iostream>
#include <memory>
#include "CreatureCard.hpp"
Meteor::Meteor() : GeneralSpell("Meteor", "METOR", 4) {}

void Meteor::execute(Player& current, Player& opponent)
{
	for(Player& player : {std::ref(current), std::ref(opponent)})
	{
		for(int i = 0; i < 7; ++i)
		{
			auto card = player.getFieldZone().getCard(i);
			auto creature = std::dynamic_pointer_cast<CreatureCard>(card);
			if(creature)
			{
				creature->takeDamage(3);
			}			
		}

		for(int i = 0; i < 7; ++i)
		{
			auto card = player.getBattleZone().getCard(i);
			auto creature = std::dynamic_pointer_cast<CreatureCard>(card);
			if(creature)
			{
				creature->takeDamage(3);
			}			
		}	

	}
}
////