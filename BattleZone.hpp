#ifndef BATTLEZONE_HPP
#define BATTLEZONE_HPP

#include <memory>
#include <array>
#include "CreatureCard.hpp"  

class BattleZone {
public:
    bool placeCard(int slotIndex, std::shared_ptr<CreatureCard> card);
    std::shared_ptr<CreatureCard> removeCard(int slotIndex);
    std::shared_ptr<CreatureCard> getCard(int slotIndex) const;
    bool isSlotEmpty(int slotIndex) const;

private:
    std::array<std::shared_ptr<CreatureCard>, 7> slots_{};
};

#endif


////