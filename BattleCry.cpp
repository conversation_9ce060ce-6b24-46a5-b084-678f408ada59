#include "BattleCry.hpp"
#include <iostream>
#include <memory>
#include <algorithm>
#include <vector>

BattleCry::BattleCry()
    : GeneralSpell("Battle Cry", "BTLCY", 3) {}

void BattleCry::execute(Player& current, Player& opponent) {
//FieldZone
	for (int i = 0; i < 7; ++i)
	{
		auto card = current.getFieldZone().getCard(i);
		auto creature = std::dynamic_pointer_cast<CreatureCard>(card);
		if (creature)
		{
			creature->setCurrentAttack(creature->getCurrentAttack() + 3);
			auto& traits = creature->getTraits();
			if (std::find(traits.begin(), traits.end(), Trait::Haste) == traits.end())
			{
				traits.push_back(Trait::Haste);
			}

			if (std::find(traits.begin(), traits.end(), Trait::Temporary) == traits.end())
			{
				traits.push_back(Trait::Temporary);
			}
			}
		}

// BattleZone
	for (int i = 0; i < 7; ++i)
	{
		auto card = current.getBattleZone().getCard(i);
		auto creature = std::dynamic_pointer_cast<CreatureCard>(card);
		if (creature)
		{
		creature->setCurrentAttack(creature->getCurrentAttack() + 3);
		auto& traits = creature->getTraits();
		if (std::find(traits.begin(), traits.end(), Trait::Haste) == traits.end())
		{
			traits.push_back(Trait::Haste);
		}

		if (std::find(traits.begin(), traits.end(), Trait::Temporary) == traits.end())
		{
			traits.push_back(Trait::Temporary);
		}

		}
	}   
}
////