// GameConfigParser.cpp
// Zweck:
//   Implementierung des Parsers für die game_config.txt
//   Liest die grundlegenden Spielparameter sowie die Kartendecks der Spieler ein

#include "GameConfigParser.hpp"
#include <fstream>
#include <sstream>
#include <iostream>

bool GameConfigParser::checkFile(const std::string& path, const std::string& magic) {
    std::ifstream file(path);
    if (!file.is_open()) {
        std::cout << "[ERROR] Invalid file (" << path << ")." << std::endl;
        return false;
    }
    std::string line;
    std::getline(file, line);
    if (line != magic) {
        std::cout << "[ERROR] Invalid file (" << path << ")." << std::endl;
        return false;
    }
    return true;
}

bool GameConfigParser::parseFullConfig(const std::string& path) {
    std::ifstream file(path);
    if (!file.is_open()) return false;

    std::string line;
    // 1) Überspringe "GAME"
    std::getline(file, line);

    // 2) Lese Health, Runden, Deck-Size, Mana-Pool-Size
    std::getline(file, line); health       = std::stoi(line);
    std::getline(file, line); maxRounds    = std::stoi(line);
    std::getline(file, line); deckSize     = std::stoi(line);
    std::getline(file, line); manaPoolSize = std::stoi(line);

    // 3) Leerzeile überspringen
    std::getline(file, line);

    // 4) Spieler 1–Deck parsen
    std::getline(file, line);
    {
        std::stringstream ss(line);
        std::string token;
        while (std::getline(ss, token, ';')) {
            if (!token.empty()) {
                deck1.push_back(token);
            }
        }
        if (static_cast<int>(deck1.size()) != deckSize) {
            std::cerr << "[ERROR] Deck1 size mismatch: expected "
                      << deckSize << ", got " << deck1.size() << std::endl;
            return false;
        }
    }

    // 5) Spieler 2–Deck parsen
    std::getline(file, line);
    {
        std::stringstream ss(line);
        std::string token;
        while (std::getline(ss, token, ';')) {
            if (!token.empty()) {
                deck2.push_back(token);
            }
        }
        if (static_cast<int>(deck2.size()) != deckSize) {
            std::cerr << "[ERROR] Deck2 size mismatch: expected "
                      << deckSize << ", got " << deck2.size() << std::endl;
            return false;
        }
    }

    return true;
}

int GameConfigParser::getHealth()        const { return health; }
int GameConfigParser::getMaxRounds()     const { return maxRounds; }
int GameConfigParser::getDeckSize()      const { return deckSize; }
int GameConfigParser::getManaPoolSize()  const { return manaPoolSize; }
std::vector<std::string> GameConfigParser::getPlayer1Deck() const { return deck1; }
std::vector<std::string> GameConfigParser::getPlayer2Deck() const { return deck2; }
