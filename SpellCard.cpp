#include "SpellCard.hpp"

SpellCard::SpellCard(std::string name, std::string id, int manaCost,    
                     std::string effect, SpellType subtype)
	: Card(name, id, manaCost), effect_(effect), subtype_(subtype), variableMana_(false){}

SpellCard::SpellCard(std::string name, std::string id,    
										 std::string effect, SpellType subtype)
	: Card(name, id, 0), effect_(effect), subtype_(subtype), variableMana_(true){}


//------------------------------------------Getter-setter
std::string SpellCard::getEffect() const
{
	return effect_;
}

SpellType SpellCard::getSubType() const
{
	return subtype_;
}

bool SpellCard::isManaVariable() const
{
	return variableMana_;
}
////