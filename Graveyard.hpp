#ifndef GRAVEYARD_HPP
#define GRAVEYARD_HPP

#include <vector>
#include <memory>
#include "CreatureCard.hpp"

class Graveyard
{
public:
    void add(std::shared_ptr<CreatureCard> creature);
    std::shared_ptr<CreatureCard> top() const;
    std::shared_ptr<CreatureCard> removeTop();
    bool isEmpty() const;
    int size() const;
    const std::vector<std::shared_ptr<CreatureCard>> &getCards() const;

private:
    std::vector<std::shared_ptr<CreatureCard>> cards_;
};

#endif
////