#ifndef TARGETSPELL_HPP
#define TARGETSPELL_HPP

#include "SpellCard.hpp"
#include "CreatureCard.hpp"
#include <memory>

class TargetSpell : public SpellCard
{
public:

    TargetSpell(const std::string& name, const std::string& id, int manaCost)
        : SpellCard(name, id, manaCost, "", SpellType::Target) {}


    TargetSpell(const std::string& name, const std::string& id, bool variableMana)
        : SpellCard(name, id, "", SpellType::Target)
    {
        if (variableMana) {

        }
    }


    virtual void applyEffect(std::shared_ptr<CreatureCard> target) = 0;

    virtual ~TargetSpell() = default;
};

#endif
///