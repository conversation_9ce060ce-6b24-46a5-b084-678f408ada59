#ifndef GAME_HPP
#define GAME_HPP

#include <string>
#include <vector>
#include "Player.hpp"
#include "CardDatabase.hpp"
#include "GameConfigParser.hpp"
#include "MessageConfigParser.hpp"
#include <set>

class Game {
public:
    Game() = default;

    bool initialize(const std::string& gameConfigPath, const std::string& messageConfigPath);
	void start();
    void resolveBattle(Player& attacker, Player& defender);

    Player& getPlayer1();
    Player& getPlayer2();

    // Новый метод
    void handleInfoCommand(const std::vector<std::string>& tokens);
    void drawBoard(const Player& defender, const Player& attacker) const;
    void handleHandCommand(const Player& player) const;
    void handleRedrawCommand(Player& current);
    void handleHelpCommand() const;
    void handleBattleCommand(const std::vector<std::string>& tko<PERSON>, Player& current, Player& opponent);
    void handleSpellCommand(const std::vector<std::string>& tokens, Player& current, Player& opponent);

    std::array<std::string, 4> layoutCardLines(const std::shared_ptr<Card>& card) const;
    void drawCardRow(const std::vector<std::shared_ptr<Card>>& cards, int lineIndex, char border) const;
private:
    CardDatabase cardDB_;
    GameConfigParser config_;
    MessageConfigParser messageConfig_; 

    Player player1_;
    Player player2_;

    bool redrawAllowedP1_ = true;
    bool redrawAllowedP2_ = true;
    bool boardEnabled_ = true;
    bool lastCommandWasBoard_ = false;

    std::set<std::shared_ptr<CreatureCard>> creatureEnteredThisRound_;
};

#endif
