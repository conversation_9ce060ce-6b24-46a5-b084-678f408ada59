#include "Mobilize.hpp"
#include <algorithm>

Mobilize::Mobilize()
    : TargetSpell("Mobilize", "MOBLZ", 2) {}

void Mobilize::applyEffect(std::shared_ptr<CreatureCard> target)
{
    target->setCurrentAttack(target->getCurrentAttack() + 1);

    std::vector<Trait> traits = target->getCurrentTraits();
    if (std::find(traits.begin(), traits.end(), Trait::Haste) == traits.end())
    {
        traits.push_back(Trait::Has<PERSON>);
        std::sort(traits.begin(), traits.end());
        target->setCurrentTraits(traits);
    }
}
////