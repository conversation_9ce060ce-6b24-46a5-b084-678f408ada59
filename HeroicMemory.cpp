#include "HeroicMemory.hpp"
#include "Trait.hpp"

HeroicMemory::HeroicMemory()
    : GraveyardSpell("Heroic Memory", "MEMRY", 0) 
{
    
}

void HeroicMemory::applyEffect(Player& player, std::shared_ptr<CreatureCard> target)
{
    if (!target)
        return;

    
    target->resetToBaseStats();

   
    auto traits = target->getCurrentTraits();
    traits.push_back(Trait::<PERSON><PERSON>);
    traits.push_back(Trait::Temporary);
    target->setCurrentTraits(traits);

    
    for (int i = 0; i < 7; ++i)
    {
        if (!player.getFieldZone().getCard(i))
        {
            player.getFieldZone().placeCard(i, target);
            break;
        }
    }
}
////