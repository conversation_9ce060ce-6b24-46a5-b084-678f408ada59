#ifndef CREATURECARD_HPP
#define CREATURECARD_HPP

#include "Card.hpp"
#include "Trait.hpp"
#include <vector>

class CreatureCard: public Card
{
private:
    int baseHealth_;
    int currentHealth_;
    int baseAttack_;
    int currentAttack_;
    std::vector<Trait> baseTraits_;
    std::vector<Trait> currentTraits_;

public:
    CreatureCard(std::string name, std::string id, int manaCost,
                 int baseHealth, int baseAttack, std::vector<Trait> baseTraits);

    int getBaseHealth() const;
    int getCurrentHealth() const;
    int getBaseAttack() const;
    int getCurrentAttack() const;
    std::vector<Trait> getBaseTraits() const;
    std::vector<Trait> getCurrentTraits() const;

    void setCurrentHealth(int health);
    void setCurrentAttack(int attack);
    void setCurrentTraits(const std::vector<Trait>& traits);

    void takeDamage(int damage);
    void resetToBaseStats();

    // ✅ Добавь это сюда:
    const std::vector<Trait>& getTraits() const;
    std::vector <Trait>& getTraits();
};

#endif
