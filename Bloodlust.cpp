#include "Bloodlust.hpp"
#include "Trait.hpp"
#include <cmath>

Bloodlust::Bloodlust()
    : TargetSpell("Bloodlust", "BLOOD", 5) {}

void Bloodlust::applyEffect(std::shared_ptr<CreatureCard> target)
{
    auto traits = target->getCurrentTraits();
    traits.push_back(Trait::Brutal);
    traits.push_back(Trait::Lifesteal);
    target->setCurrentTraits(traits);

    int health = target->getCurrentHealth();
    int newHealth = static_cast<int>(std::ceil(health / 2.0));
    target->setCurrentHealth(newHealth);
}////
