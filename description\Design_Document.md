# Design Document

The purpose of writing a design document is to first understand the assignment description and the game. You should decide on the basic structure and design of your program. You will then discuss your design with other teams in a discussion round. If you later notice that you need to change something in the structure, that is of course possible.

The aim of the discussion round is to help you decide on a well-structured design early on in the implementation process. Taking part in a discussion round is mandatory. It is important that every team member participates during the discussion.

> **Note:** Handing in the design document is not worth any points, but it is **mandatory** to submit a document that shows some effort to receive a positive grade on the practicals (KU).

## Content of the Design Document

The design document should include information about the following:
- the classes you will use for your implementation
- a short description (1-2 sentences or keywords) of the purpose of each class
- a list of the most important methods and member variables of each class - it is not necessary to provide the full function headers and data types
- relations between the classes (e.g. inheritance, composition)

You should also visualize your design using a **UML diagram**. Simple drawings by hand or digital are fine. It is important that the graphic helps to understand the structure of your program. The visualization does not need to be extremely detailed.

## Questions to ask yourself

Below are a few questions to think about and discuss with your team when deciding on the design. This list is non-exhaustive, you also need to come up with your own ideas.

- What is the overall class structure? How do the objects interact with each other?
- How are the players, board and cards represented?
- How is the round structured?
- How are the messages stored internally?
- How are the card decks stored internally?
- How are the hand cards stored internally?
- How are commands read in and handled?
- ...