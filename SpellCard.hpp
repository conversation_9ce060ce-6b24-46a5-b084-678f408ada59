#ifndef SPELLCARD_HPP
#define SPELLCARD_HPP

#include "Card.hpp"

enum class SpellType
{
  General,
  Target,
  Graveyard
};

class SpellCard: public Card
{
  private:
    std::string effect_;
    SpellType subtype_;
    bool variableMana_;

  public:
    //constructor for fixed mana costs
    SpellCard(std::string name, std::string id, int manaCost,    
              std::string effect, SpellType subtype);
    //constructor for variable mana costs
    SpellCard(std::string name, std::string id,
              std::string effect, SpellType subtype);
      
    std::string getEffect() const;
    SpellType getSubType() const;
    bool isManaVariable() const;
    
};

#endif
////