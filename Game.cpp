// Zweck:
// Implementierung der Game-Klasse
// Beinhaltet die Logik zum Überprüfen und Einlesen der Konfigurationsdateien beim Start des Spiels

// Bereits implementiert:
//     Auf<PERSON><PERSON> von GameConfigParser::checkFile(...) zur Validierung der GAME-Konfiguration
//     Auf<PERSON><PERSON> von MessageConfigParser::checkFile(...) zur Validierung der MESSAGE-Konfiguration
//     Rückgabewerte bei Fehlern werden korrekt behandelt (false → Exit-Code 3)

#include <sstream>

#include "Trait.hpp"

#include "GeneralSpell.hpp"
#include "GraveyardSpell.hpp"
#include "TargetSpell.hpp"
#include "Game.hpp"
#include "MessageConfigParser.hpp"
#include <iostream>
#include <iomanip>
#include <algorithm>
bool Game::initialize(const std::string& gameConfigPath, const std::string& messageConfigPath)
{
    // Проверка и разбор конфига игры
    if (!config_.checkFile(gameConfigPath, "GAME"))
        return false;

    if (!config_.parseFullConfig(gameConfigPath)) {
        std::cout << "[ERROR] Failed to parse game config." << std::endl;
        return false;
    }

    // Проверка и инициализация сообщений
    messageConfig_ = MessageConfigParser();
    if (!messageConfig_.checkFile(messageConfigPath, "MESSAGE"))
        return false;

		if(!messageConfig_.parseMessages(messageConfigPath))
		{
			std::cout << "[ERROR] Failed to parse message config." << std::endl;
			return false;
		}

    // Создаём игроков с начальными параметрами
    player1_ = Player(
        config_.getPlayer1Deck(),
        cardDB_,
        config_.getHealth(),
        config_.getManaPoolSize()
    );
    player2_ = Player(
        config_.getPlayer2Deck(),
        cardDB_,
        config_.getHealth(),
        config_.getManaPoolSize()
    );

    // === Новый функционал для начала игры ===

    // 1) Перемешиваем их колоды
   // player1_.shuffleDeck();
   // player2_.shuffleDeck();

    // 2) Раздаём по 6 стартовых карт
    for (int i = 0; i < 7; ++i) {
        player1_.drawCard();
        player2_.drawCard();
    }

    // 3) Восполняем ману до полного пула
    player1_.refillMana();
    player2_.refillMana();
	
		//DEBUG
	/*	std::cout << "P1 handakrten\n";
		for (auto& c : player1_.getHand())
			std::cout << "P1 hat:" << c->getId() << "\n";

		std::cout << "P2 handakrten\n";
		for (auto& c : player2_.getHand())
			std::cout << "P2 hat:" << c->getId() << "\n";
*/

    return true;
}


void Game::resolveBattle(Player& attacker, Player& defender) ///ATTACK KAMPFLOGIK
{
    for(int slot = 0; slot < 7; ++slot)
    {
        auto atkCard = attacker.getBattleZone().getCard(slot);
        if(!atkCard) continue;

        auto defCard = defender.getBattleZone().getCard(slot);

        if(defCard)
        {
            //Beide Kreaturen greifen ann hemen schaden
            defCard->setCurrentHealth(defCard->getCurrentHealth() - atkCard->getCurrentAttack());
            atkCard->setCurrentHealth(atkCard->getCurrentHealth() - defCard->getCurrentAttack());

            //Verteidiger stirbt und wird zum Friedhof hinzugefügt
            if(defCard->getCurrentHealth() <= 0)
            {
                defender.getBattleZone().removeCard(slot);
                defender.getGraveyard().add(atkCard);
            }

            //angreifer stribt
            if(atkCard->getCurrentHealth() <= 0)
            {
                attacker.getBattleZone().removeCard(slot);
                attacker.getGraveyard().add(atkCard);
            }
        }
        else // DIREKTER ANGIRFF AUF SPIELER
        {
            defender.changeHealth(-(atkCard->getCurrentAttack())); //minus da weniger wird
        }
    }
}

Player& Game::getPlayer1()
{
    return player1_;
}

Player& Game::getPlayer2()
{
    return player2_;
}////



void Game::handleInfoCommand(const std::vector<std::string>& tokens) {
    if (tokens.size() != 2) {
        std::cout << messageConfig_.getMessage("E_INVALID_PARAM_COUNT") << std::endl;
        return;
    }

    std::string cardId = tokens[1];
    std::transform(cardId.begin(), cardId.end(), cardId.begin(), ::toupper);
    auto card = cardDB_.getCardById(cardId);
    if (!card) {
        std::cout << messageConfig_.getMessage("E_INVALID_CARD") << std::endl;
        return;
    }

    std::cout << messageConfig_.getMessage("D_BORDER_INFO") << std::endl;
    std::cout << card->getName() << " [" << card->getId() << "] (" << card->getManaCost() << " mana)" << std::endl;

    if (auto creature = std::dynamic_pointer_cast<CreatureCard>(card)) {
        std::cout << "Type: Creature" << std::endl;
        std::cout << "Base Attack: " << creature->getBaseAttack() << std::endl;
        std::cout << "Base Health: " << creature->getBaseHealth() << std::endl;

        auto traits = creature->getTraits();
        if (traits.empty()) {
            std::cout << "Base Traits: -" << std::endl;
        } else {
            std::cout << "Base Traits: ";
            for (size_t i = 0; i < traits.size(); ++i) {
                std::cout << traits[i];
                if (i != traits.size() - 1) std::cout << ", ";
            }
            std::cout << std::endl;
        }
    } else if (auto spell = std::dynamic_pointer_cast<SpellCard>(card)) {
        std::cout << "Type: Spell" << std::endl;
        std::string effectKey = "D_" + spell->getId();
        std::cout << "Effect: " << messageConfig_.getMessage(effectKey) << std::endl;
    }

    std::cout << messageConfig_.getMessage("D_BORDER_D") << std::endl;
}

void Game::start() {
    int round = 1;
    bool running = true;

    // Приветствие
    std::cout << "=========================================================================================" << std::endl;
    std::cout << "Welcome to Magical OOPerations. Are you ready to OOPtimize your strategy?" << std::endl;
    std::cout << "=========================================================================================" << std::endl;

    while (running) {
        // Заголовок раунда
        std::cout << "\n=========================================================================================" << std::endl;
        std::cout << "                                         ROUND " << round << std::endl;
        std::cout << "=========================================================================================" << std::endl;

        // На нечётных раундах увеличиваем пул маны и заполняем
        if (round % 2 == 1) {
            player1_.increaseManaPoolSize();
            player2_.increaseManaPoolSize();
            player1_.refillMana();
            player2_.refillMana();
        }

        // Определяем роли в этом раунде
int grp = round / 2;
const std::string defHeader = (grp % 2 == 0) ? "PLAYER 2" : "PLAYER 1";
const std::string atkHeader = (defHeader == "PLAYER 2") ? "PLAYER 1" : "PLAYER 2";
        // Два хода: i=0 → первый игрок, i=1 → второй
        for (int i = 0; i < 2 && running; ++i) {
					bool p1FirstThisRound = (round == 1 ||( (round-2) /2) %2 == 1);
   Player& current = (i == 0)
       ? (p1FirstThisRound ? player1_ : player2_)
       : (p1FirstThisRound ? player2_ : player1_);
   Player& opponent = (i == 0)
      ? (p1FirstThisRound ? player2_ : player1_)
       : (p1FirstThisRound ? player1_ : player2_);

            // Перед ходом рисуем поле только для первого игрока хода
  if (i == 0) {
                if (boardEnabled_) {
                   std::cout << "================================== DEFENDER: " << defHeader
                              << " ===================================" << std::endl;
                    drawBoard(opponent, current);
                    std::cout << "================================== ATTACKER: " << atkHeader
                              << " ===================================" << std::endl << std::endl;
               } else {
                   // доска выключена — но оставить ровно одну пустую строку перед prompt
                    std::cout << std::endl;
                }
            }


            // Обрабатываем команды
            bool turnOver = false;
            while (!turnOver && running) {
            std::cout << ((((round / 2) % 2 == 0) ^ (i == 1)) ? "P1" : "P2") << "> "; //changed condition

                std::string input;
                std::getline(std::cin, input);

                if (input == "quit") {
                    running = false;
                    break;
                }
                else if (input == "status") {
                    // ===== Status Command =====
                    std::cout << "=== Status " << std::string(89 - 11, '=') << "\n";
                    // Player 1
                    std::cout << "Player 1\n"
                              << "Role: " << ((((round / 2) % 2 == 0) ^ (i == 1)) ? "Attacker" : "Defender") << "\n" // changed condiiton
                              << "Health: " << player1_.getHealth() << "\n"
                              << "Mana: " << player1_.getCurrentMana()
                              << " / " << player1_.getManaPoolSize() << "\n"
                              << "Remaining Deck: " << player1_.getDeckSize() << " card(s)\n"
                              << "Graveyard Size: " << player1_.getGraveyard().size() << " card(s)\n"
                              << std::string(89, '-') << "\n"
                              // Player 2
                              << "Player 2\n"
                              << "Role: " << ((((round / 2) % 2 == 0) ^ (i == 1)) ? "Defender" : "Attacker") << "\n" //changed condition
                              << "Health: " << player2_.getHealth() << "\n"
                              << "Mana: " << player2_.getCurrentMana()
                              << " / " << player2_.getManaPoolSize() << "\n"
                              << "Remaining Deck: " << player2_.getDeckSize() << " card(s)\n"
                              << "Graveyard Size: " << player2_.getGraveyard().size() << " card(s)\n"
                              << std::string(89, '=') << "\n\n";
                }
                else if (input.rfind("info", 0) == 0) {
                    std::vector<std::string> tokens;
                    std::istringstream iss(input);
                    std::string word;
                    while (iss >> word) tokens.push_back(word);
                    handleInfoCommand(tokens);
                    std::cout << "\n";
                }
								else if (input == "board") {
										// разбиваем на токены
										std::istringstream iss(input);
										std::vector<std::string> tokens;
										std::string word;
										while (iss >> word) tokens.push_back(word);

										if (tokens.size() != 1) {
												// E_INVALID_PARAM_COUNT
												std::cout << messageConfig_.getMessage("E_INVALID_PARAM_COUNT") 
																	<< std::endl << std::endl;
										} else {
												boardEnabled_ = !boardEnabled_;
										  lastCommandWasBoard_ = true;
												if (boardEnabled_) {
														// при включении – печатаем доску сразу, с одним пустым передом
														std::cout 
																			<< "================================== DEFENDER: " << defHeader
																			<< " ===================================" << std::endl;
														drawBoard(opponent, current);
														std::cout << "================================== ATTACKER: " << atkHeader
																			<< " ===================================" << std::endl <<std::endl;
												}
												else if(!boardEnabled_)
												{
														// при выключении – ровно одна пустая строка
														std::cout << std::endl;
												}
										}
								}


							else if (input == "done") {
									// 1) Текущий игрок берёт карту
									if (lastCommandWasBoard_ && !boardEnabled_)
									{
											std::cout << std::endl;
											lastCommandWasBoard_ = false;
									}
									current.drawCard();

									// 2) Перерисовываем поле, если boardEnabled_
									if (boardEnabled_) {
											std::cout 
																<< "================================== DEFENDER: " << defHeader
																<< " ===================================" << std::endl;

											bool skipP2Starts = ((round - 2) % 4 == 0) || ((round - 3) % 4 == 0);
											std::string lastPlayer = 
													((((round / 2) % 2 == 0) ^ (i == 1)) ? "P1" : "P2");

											if (lastPlayer == "P2" && !skipP2Starts) {
													drawBoard(current, opponent);
											} else {
													drawBoard(opponent, current);
											}

											std::cout << "================================== ATTACKER: " << atkHeader
																<< " ===================================" << std::endl << std::endl;
									}


									// 3) Завершаем ход
									turnOver = true;
									if (&current == &player1_) redrawAllowedP1_ = false;
									else                  redrawAllowedP2_ = false;

									// 4) Боевая фаза (всегда с одним пустым передом)
									if (i == 1) {
											if (!boardEnabled_) 
											std::cout << std::endl;
											std::cout 
																<< "===================================== BATTLE PHASE ======================================" << std::endl;
											for (int slot = 1; slot <= 7; ++slot) {
													std::cout << "---------------------------------------- SLOT "
																		<< slot
																		<< " -----------------------------------------" << std::endl;
											}
											std::cout << "-------------------------------------- BATTLE END ---------------------------------------" << std::endl;
									}
							}

								
								else if(input.rfind("redraw", 0) == 0)
								{
									std::istringstream iss(input);
									std::vector<std::string> tokens;
									std::string word;
									while (iss >> word) tokens.push_back(word);

									if (tokens.size() != 1)
									{
										std::cout << messageConfig_.getMessage("E_INVALID_PARAM_COUNT") << "\n\n";
										continue;
									}

									bool& allowed = (&current == &player1_) ? redrawAllowedP1_ : redrawAllowedP2_;

									if (!allowed)
									{
										std::cout << messageConfig_.getMessage("E_REDRAW_DISABLED") << "\n\n";
										continue;
									}

									if (current.getHandSize() <= 1)
									{
										std::cout << messageConfig_.getMessage("E_REDRAW_NOT_ENOUGH_CARDS") << "\n\n";
										continue;
									}
									handleRedrawCommand(current);
								}

                else if (input.rfind("creature",0) == 0)
                {
									std::vector<std::string> tokens;
									std::istringstream iss(input);
									std::string temp;
									while(iss >> temp) tokens.push_back(temp);

									if (tokens.size()!=3)
									{
										std::cout <<"[ERROR] " <<  messageConfig_.getMessage("E_INVALID_PARAM_COUNT") <<"\n\n";
										continue;
									}
									std::string cardId = tokens [1];
									std::transform(cardId.begin(), cardId.end(), cardId.begin(), ::toupper);
									auto card = current.findCardInHand(cardId);
									std::string slotStr = tokens[2];

									if(!cardDB_.getCardById(cardId))
									{
										std::cout <<"[ERROR] " <<  messageConfig_.getMessage("E_INVALID_CARD") <<"\n\n";
										continue;
									}
									int slot = slotStr[1] - '1'; //ACHTUNG wichtig f1 --> 0 
									
									if(slot < 0 || slot >= 7 || tokens[2].length() != 2)
									{
										std::cout <<"[ERROR] " <<  messageConfig_.getMessage("E_INVALID_SLOT") <<"\n\n";
										continue;
									}
									if(slotStr.length() !=2 || slotStr[0] != 'f' || !isdigit(slotStr[1]))
									{
										std::cout <<"[ERROR] " <<  messageConfig_.getMessage("E_NOT_IN_FIELD") <<"\n\n";
										continue;
									}	
									if(!card)
									{
										std::cout <<"[ERROR] " <<  messageConfig_.getMessage("E_NOT_IN_HAND") <<"\n\n";
										continue;
									}

									auto creature = std::dynamic_pointer_cast<CreatureCard>(card);
									if(!creature)
									{
										std::cout <<"[ERROR] " <<  messageConfig_.getMessage("E_NOT_CREATURE") <<"\n\n";
										continue;
									}
									if(current.getFieldZone().getCard(slot))
									{
										std::cout <<"[ERROR] " <<  messageConfig_.getMessage("E_FIELD_OCCUPIED") <<"\n\n";
										continue;
									}

									if(!current.spendMana(creature->getManaCost()))
									{
										std::cout <<"[ERROR] " <<  messageConfig_.getMessage("E_NOT_ENOUGH_MANA") <<"\n\n";
										continue;
									}

									current.removeCardFromGraveyard(cardId);
									current.getFieldZone().placeCard(slot, creature);
									creatureEnteredThisRound_.insert(creature);

									//Finde info Message
									std::string messageKey = "I_" + cardId;
									std::cout <<"[INFO] " << messageConfig_.getMessage(messageKey) << "\n\n";

                }
                else if(input.rfind("hand", 0) == 0 )
                {
                    std::istringstream iss(input);
                    std::vector<std::string> tokens;
                    std::string word;
                    while(iss >> word)
                    {
											tokens.push_back(word);
                    }
                    if(tokens.size()!= 1)
                    {
											std::cout <<  messageConfig_.getMessage("E_INVALID_PARAM_COUNT") << "\n\n";
											continue;
                    }
                    handleHandCommand(current);
                }
                else if(input.rfind("help", 0) == 0)
                {
									std::istringstream iss(input);
									std::vector<std::string> tokens;
									std::string word;
									while(iss >> word)
									{
										tokens.push_back(word);
									}
									if(tokens.size()!= 1)
									{
										std::cout <<  messageConfig_.getMessage("E_INVALID_PARAM_COUNT") << "\n\n";
										continue;
									}
									handleHelpCommand();
                }   
                else if(input.rfind("battle", 0) == 0)
                {
									std::vector<std::string> tokens;
									std::istringstream iss(input);
									std::string temp;
									while (iss >> temp) tokens.push_back(temp);
									handleBattleCommand(tokens, current, opponent);
									std::cout << "\n";
                }
								else if (input.rfind("spell", 0) == 0)
								{
									std::vector<std::string> tokens;
									std::istringstream iss(input);
									std::string word;
									while (iss >> word) tokens.push_back(word);
									handleSpellCommand(tokens, current, opponent);
								}
                else
								{
									std::cout << "[INFO] Unknown command.\n\n";
                }
            }
        } // конец for(i)

        // Следующий раунд
        ++round;
				creatureEnteredThisRound_.clear();
    } // конец while(running)
}//Hier start noch bisschen aufräuumen die ganzen error-Meldungen noch rausgeben hab egrade keine zeit dafür

void Game::handleSpellCommand(const std::vector<std::string>& tokens, Player& current, Player& opponent)
{
	if (tokens.size() < 2)
	{
		std::cout << messageConfig_.getMessage("E_MISSING_CARD") << "\n\n";
		return;
	}

	std::string cardId = tokens[1];
	std::transform(cardId.begin(), cardId.end(), cardId.begin(), ::toupper);

	auto card = cardDB_.getCardById(cardId);
	if (!card)
	{
		std::cout << messageConfig_.getMessage("E_INVALID_CARD") << "\n\n";
		return;
	}

	auto handCard = current.findCardInHand(cardId);
	if (!handCard)
	{
		std::cout << messageConfig_.getMessage("E_NOT_IN_HAND") << "\n\n";
		return;
	}

	auto spell = std::dynamic_pointer_cast<SpellCard>(handCard);
	if (!spell)
	{
			std::cout << messageConfig_.getMessage("E_NOT_SPELL") << "\n\n";
			return;
	}

	if (!current.spendMana(spell->getManaCost()))
	{
		std::cout << messageConfig_.getMessage("E_NOT_ENOUGH_MANA") << "\n\n";
		return;
	}

	SpellType subtype = spell->getSubType();

	if (subtype == SpellType::General)
	{
		if (tokens.size() != 2)
		{
			std::cout << messageConfig_.getMessage("E_INVALID_PARAM_COUNT_SPELL") << "\n\n";
			return;
		}

		auto generalSpell = std::dynamic_pointer_cast<GeneralSpell>(spell);
		if (generalSpell)
		{
			generalSpell->execute(current, opponent);
		}

		std::cout << "[INFO] " << messageConfig_.getMessage("I_" + cardId) << "\n\n";
	}

	else if (subtype == SpellType::Target)
	{
		if (tokens.size() != 3)
		{
			std::cout << messageConfig_.getMessage("E_INVALID_PARAM_COUNT_SPELL") << "\n\n";
			return;
		}

		std::string slot = tokens[2];
		std::transform(slot.begin(), slot.end(), slot.begin(), ::toupper);

		bool isOpponent = false;
		if (slot[0] == 'O' && slot.size() == 3)
		{
			isOpponent = true;
			slot = slot.substr(1);
		}

		if (slot.size() != 2 || (slot[0] != 'F' && slot[0] != 'B') || !isdigit(slot[1]) || slot[1] < '1' || slot[1] > '7')
		{
			std::cout << messageConfig_.getMessage("E_INVALID_SLOT_SPELL") << "\n\n";
			return;
		}

		int index = slot[1] - '1';
		Player& targetPlayer = isOpponent ? opponent : current;

		std::shared_ptr<Card> targetCard = (slot[0] == 'F')
				? targetPlayer.getFieldZone().getCard(index)
				: targetPlayer.getBattleZone().getCard(index);

		if (!targetCard)
		{
			std::cout << messageConfig_.getMessage("E_TARGET_EMPTY") << "\n\n";
			return;
		}

		auto creatureTarget = std::dynamic_pointer_cast<CreatureCard>(targetCard);
		if (!creatureTarget)
		{
			std::cout << messageConfig_.getMessage("E_TARGET_EMPTY") << "\n\n";
			return;
		}

		auto targetSpell = std::dynamic_pointer_cast<TargetSpell>(spell);
		if (targetSpell)
		{
			targetSpell->applyEffect(creatureTarget);
		}

		std::cout << "[INFO] " << messageConfig_.getMessage("I_" + cardId) << "\n\n";
	}

	else if (subtype == SpellType::Graveyard)
	{
		if (tokens.size() != 3)
		{
			std::cout << messageConfig_.getMessage("E_INVALID_PARAM_COUNT_SPELL") << "\n\n";
			return;
		}

		std::string graveyardId = tokens[2];
		std::transform(graveyardId.begin(), graveyardId.end(), graveyardId.begin(), ::toupper);

		const auto& graveyardCards = current.getGraveyard().getCards();
		auto it = std::find_if(graveyardCards.begin(), graveyardCards.end(), [&](const std::shared_ptr<Card>& c)
		{
			return c->getId() == graveyardId;
		});

		if (it == graveyardCards.end())
		{
			std::cout << messageConfig_.getMessage("E_NOT_IN_GRAVEYARD") << "\n\n";
			return;
		}

			auto creature = std::dynamic_pointer_cast<CreatureCard>(*it);
		if (!creature)
		{
			std::cout << messageConfig_.getMessage("E_NOT_IN_GRAVEYARD") << "\n\n";
			return;
		}

		auto graveyardSpell = std::dynamic_pointer_cast<GraveyardSpell>(spell);
		if (graveyardSpell)
		{
			graveyardSpell->applyEffect(current, creature);
		}

		std::cout << "[INFO] " << messageConfig_.getMessage("I_" + cardId) << "\n\n";
	}
	else
	{
		std::cout << "[ERROR] Unknown Spell-TYPE.\n\n";
	}
}


void Game::handleBattleCommand(const std::vector<std::string>& tokens, Player& current, Player& opponent)
{
	if(tokens.size() != 3)
	{
		std::cout << messageConfig_.getMessage("E_INVALID_PARAM_COUNT") << "\n\n";
		return;
	}

	std::string fieldZoneSlot = tokens[1];
	std::string battleZoneSlot = tokens[2];
	std::transform(fieldZoneSlot.begin(),fieldZoneSlot.end(),fieldZoneSlot.begin(), ::toupper);
	std::transform(battleZoneSlot.begin(),battleZoneSlot.end(),battleZoneSlot.begin(), ::toupper);

	if (!(fieldZoneSlot.size() == 2 &&
      (std::toupper(fieldZoneSlot[0]) == 'F' ) &&
			 fieldZoneSlot[1] >= '1' && fieldZoneSlot[1] <= '7') ||
    !(battleZoneSlot.size() == 2 &&
      (std::toupper(battleZoneSlot[0]) == 'B') &&
			battleZoneSlot[1] >= '1' && battleZoneSlot[1] <= '7'))
	{
		std::cout << messageConfig_.getMessage("E_INVALID_SLOT") << "\n\n";
	}

	if(fieldZoneSlot[0] != 'F')
	{
		std::cout << messageConfig_.getMessage("E_NOT_IN_FIELD") << "\n\n";
	}

	int fieldZoneIndex = fieldZoneSlot[1] - '1';
	auto getCreature = current.getFieldZone().getCard(fieldZoneIndex);

	if (!getCreature)
	{
		std::cout << messageConfig_.getMessage("E_FIELD_EMPTY") << "\n\n";
		return;
	}

	auto pointerToCreature = std::dynamic_pointer_cast<CreatureCard>(getCreature);

	bool justEntered = creatureEnteredThisRound_.count(pointerToCreature);
	bool hasHaste = std::find(pointerToCreature->getTraits().begin(), pointerToCreature->getTraits().end(), Trait::Haste) != pointerToCreature->getTraits().end();
	
	if (justEntered && !hasHaste)
	{
		std::cout << messageConfig_.getMessage("E_CREATURE_CANNOT_BATTLE") << "\n\n";
		return;
	}

	if(battleZoneSlot[0] != 'B')
	{
		std::cout << messageConfig_.getMessage("E_NOT_IN_BATTLE") << "\n\n";
		return;
	}

	int battleZoneIndex = battleZoneSlot[1] - '1';
	if(current.getBattleZone().getCard(battleZoneIndex))
	{
		std::cout << messageConfig_.getMessage("E_BATTLE_OCCUPIED") << "\n\n";
		return;
	}

	current.getFieldZone().removeCard(fieldZoneIndex);
	current.getBattleZone().placeCard(battleZoneIndex, pointerToCreature);
	if(justEntered && hasHaste)
	{
		std::cout << "[INFO] " << messageConfig_.getMessage("I_HASTE") << "\n";
	}

	bool  hasChallenger = std::find(pointerToCreature->getTraits().begin(), pointerToCreature->getTraits().end(), Trait::Challenger) != pointerToCreature->getTraits().end();
	if(hasChallenger)
	{
		auto opponentFieldCard = opponent.getFieldZone().getCard(battleZoneIndex);
		auto opponentBattleCard = opponent.getBattleZone().getCard(battleZoneIndex);

		if(opponentFieldCard && !opponentBattleCard)
		{
			auto opponentCreatzure = std::dynamic_pointer_cast<CreatureCard>(opponentFieldCard);
			if(opponentCreatzure)
			{
				opponent.getFieldZone().removeCard(battleZoneIndex);
				opponent.getBattleZone().placeCard(battleZoneIndex, opponentCreatzure);
				std::cout << messageConfig_.getMessage("I_CHALLENGER") << "\n\n";
			}
		}
	}

}

void Game::handleHelpCommand() const
{
    std::cout << "=== Commands ============================================================================" << "\n";
		std::cout << ""
		<< "- help\n"
		<< "    Prints this help text.\n\n"
		<< "- quit\n"
		<< "    Terminates the game.\n\n"
		<< "- battle <FIELD_SLOT> <BATTLE_SLOT>\n"
		<< "    Moves a creature from a Field Zone slot into a Battle Zone slot.\n"
		<< "    <FIELD_SLOT>: Current slot of the creature (F1, ..., F7)\n"
		<< "    <BATTLE_SLOT>: Battle slot for the creature (B1, ..., B7)\n\n"
		<< "- board\n"
		<< "    Toggles the board printing.\n\n"
		<< "- creature <HAND_CARD_ID> <FIELD_SLOT>\n"
		<< "    Places a creature from your hand into a Field Zone slot.\n"
		<< "    <HAND_CARD_ID>: The ID of the creature card in your hand\n"
		<< "    <FIELD_SLOT>: The Field Zone slot to place the creature in\n\n"
		<< "- done\n"
		<< "    Ends your turn for this round.\n\n"
		<< "- graveyard\n"
		<< "    Prints all the cards in your graveyard.\n\n"
		<< "- hand\n"
		<< "    Prints your hand cards.\n\n"
		<< "- info <CARD_ID>\n"
		<< "    Prints card information.\n"
		<< "    <CARD_ID>: The ID of the card to be inspected\n\n"
		<< "- redraw\n"
		<< "    Discards all hand cards and draws the same amount minus one from your deck.\n\n"
		<< "- spell <HAND_CARD_ID> [<TARGET_SLOT>|<GRAVEYARD_CARD_ID>]\n"
		<< "    Casts a spell from your hand.\n"
		<< "    <HAND_CARD_ID>: The ID of the spell in your hand\n"
		<< "    <TARGET_SLOT>: The slot to target with a target spell\n"
		<< "    <GRAVEYARD_CARD_ID>: The ID of a card in the graveyard to cast a graveyard spell on\n\n"
		<< "- status\n"
		<< "    Prints general information about both players.\n\n"
		<< "=========================================================================================\n\n";
}

void Game::handleRedrawCommand(Player& current)
{
	std::vector<std::shared_ptr<Card>> oldHand = current.getHand();
	int oldSize = static_cast<int>(oldHand.size());

	for (const auto& card : oldHand)
	{
		current.addCardToDeckBottom(card);
	}

	current.clearHand();

	int drawAmount = oldSize - 1;
	drawAmount = std::max(0, drawAmount);

	for (int i = 0; i < drawAmount; ++i)
	{
		current.drawCard();
	}

	std::cout << "\n";
}

void Game::handleHandCommand(const Player& player) const
{
    const auto& hand = player.getHand();
    std::cout << "=== Hand Cards " << std::string(74, '=') << "\n";

    if (hand.empty())
		{
        std::cout << "Your hand is empty.\n";
        return;
    }

    auto getMana = [](int cost)
		{
        if (cost == 0) return std::string("MXX");
        if (cost < 10) return std::string("M0") + std::to_string(cost);
        return std::string("M") + std::to_string(cost);
    };

    std::vector<std::string> line1, line2, line3, line4;
    for (const auto& card : hand)
		{
					std::ostringstream l1;
					std::ostringstream l2;
					std::ostringstream l3;
					std::ostringstream l4;

        std::string mana = getMana(card->getManaCost());
        std::string id = card->getId();

        l1 << "_____" << mana;
        l2 << "| " << std::setw(5) << std::left << id << " |";

        if (auto creature = std::dynamic_pointer_cast<CreatureCard>(card))
				{
            std::string traits;
            for (Trait t : creature->getTraits())
                traits += to_string_board(t);
	
						l3 << "| " << std::setw(5) << std::left << traits << " |";


            l4 << "A" << std::setw(2) << std::setfill('0') << creature->getCurrentAttack()
               << "___H" << std::setw(2) << std::setfill('0') << creature->getCurrentHealth();
        }
				else
				{
            l3 << "|       |";
            l4 << " _______ ";
        }
        line1.push_back(l1.str());
        line2.push_back(l2.str());
        line3.push_back(l3.str());
        line4.push_back(l4.str());
    }
    auto join = [](const std::vector<std::string>& parts, int spacing)
		{
        std::ostringstream result;
        result << "    "; // Anfangseinrückung 4 Leerzeichen
        for (size_t i = 0; i < parts.size(); ++i) {
            result << parts[i];
            if (i + 1 != parts.size()) result << std::string(spacing, ' ');
        }
        return result.str();
    };

    std::cout << " " << join(line1, 4) << "\n";
    std::cout << join(line2, 3) << "\n";
    std::cout << join(line3, 3) << "\n";
    std::cout << join(line4, 3) << "\n";
    std::cout << std::string(89, '=') << "\n\n";
}

std::array<std::string, 4> Game::layoutCardLines(const std::shared_ptr<Card>& card) const
{
    std::ostringstream l1;
    std::ostringstream l2;
    std::ostringstream l3;
    std::ostringstream l4;
		
		std::string mana = (card->getManaCost() == 0 ) ? "MXX"
										 : (card ->getManaCost() < 10 ? "M0" + std::to_string(card->getManaCost())
										 															 : "M" + std::to_string(card->getManaCost()));
		std::string id = card->getId();
		
		l1 << "_____" << mana;
    l2 << "| " << std::setw(5) << std::left << id << " |";

    if (auto creature = std::dynamic_pointer_cast<CreatureCard>(card))
		{
			std::string traits;
			for (Trait t : creature->getTraits())
				traits += to_string_board(t);
			l3 << "| " << std::setw(5) << std::left << traits << " |";
			l4 << "A" << std::setw(2) << std::setfill('0') << creature->getCurrentAttack()
				 << "___H" << std::setw(2) << std::setfill('0') << creature->getCurrentHealth();
    }
		else
		{
			l3 << "|       |";
			l4 << " _______ ";
    }

    auto pad = [](std::string s)
		{
			if (s.length() < 11 - 1)
				s.append(11 - 1 - s.length(), ' ');
			return " " + s;
    };

    auto padShifted = [](std::string s)
		{
			if (s.length() < 11 - 2)
				s.append(11 - 2 - s.length(), ' ');
			return "  " + s;
    };

    return
		{
        padShifted(l1.str()),
        pad(l2.str()),
        pad(l3.str()),
        pad(l4.str())
    };
}

void Game::drawCardRow(const std::vector<std::shared_ptr<Card>>& cards, int lineIndex, char border) const
{
	std::cout << border << "  ";

	for(int i = 0; i < 7; ++i)
	{
		if(cards[i])
		{
			auto lines = layoutCardLines(cards[i]);
			std::cout << lines[lineIndex];
		}
		else
		{
			std::cout << std::string(11, ' ');
		}

		if(i < 6)
			std::cout << " ";
	}
	std::cout << "  " << border << "\n";
}

void Game::drawBoard(const Player& defender, const Player& attacker) const
{
	const std::string separator = "===[---------]=[---------]=[---------]=[---------]=[---------]=[---------]=[---------]===\n";
	const std::string slotNumberLine = "~~~[~~~ 1 ~~~]~[~~~ 2 ~~~]~[~~~ 3 ~~~]~[~~~ 4 ~~~]~[~~~ 5 ~~~]~[~~~ 6 ~~~]~[~~~ 7 ~~~]~~~\n";

	auto drawFieldZone = [&](const FieldZone& zone)
	{
		for (int row = 0; row < 4; ++row)
		{
			std::vector<std::shared_ptr<Card>> cards;
			for (int i = 0; i < 7; ++i)
			{
				cards.push_back(zone.getCard(i));
			}
			drawCardRow(cards, row, 'F');
		}
	};

	auto drawBattleZone = [&](const BattleZone& zone)
	{
		for (int row = 0; row < 4; ++row)
		{
			std::vector<std::shared_ptr<Card>> cards;
			for (int i = 0; i < 7; ++i)
			{
				cards.push_back(zone.getCard(i));
			}
			drawCardRow(cards, row, 'B');
		}
	};

	drawFieldZone(defender.getFieldZone());
	std::cout << separator;
	drawBattleZone(defender.getBattleZone());
	std::cout << slotNumberLine;
	drawBattleZone(attacker.getBattleZone());
	std::cout << separator;
	drawFieldZone(attacker.getFieldZone());
}
